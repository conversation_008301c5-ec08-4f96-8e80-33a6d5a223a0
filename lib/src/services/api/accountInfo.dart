import 'package:waimai_e_fe_flutter_finance/src/services/model/assetAccountInfo.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/balanceWithdraw.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// AccountInfo
Future<AssetAccountInfoModel> fetchAccountInfo(Map<String, dynamic> params) {
  return comGetApi(
          path: '/finance/waimai/account/api/poiAccountBaseInfo',
          params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return AssetAccountInfoModel.fromJson(response?.data);
  });
}

Future<AssetAccountInfoModel> fetchSchoolAccountInfo(
    Map<String, dynamic> params) {
  return comGetApi(
          path: '/finance/waimai/money/channel/poiAccountBaseInfo',
          params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return AssetAccountInfoModel.fromJson(response?.data);
  });
}

// 灰度接口--金服对接外卖提现页面
Future<bool> checkPoiIdGray(String poiId, {int appSourceType}) {
  final Map<String, dynamic> params = {'poiId': poiId};
  if (appSourceType != null) {
    params['appSourceType'] = appSourceType;
  }
  
  return comGetApi(
    path: '/finance/v4/h5/api/withdraw/checkPoiIdGray',
    params: params,
    useCommonParam: false,
  ).then((response) {
    if (response?.data == null) {
      return false;
    }
    return response?.data ?? false;
  }).catchError((error) {
    return false;
  });
}

/// 获取提现信息
Future<WithdrawInfoModal> fetchWithdrawInfoSilent(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/withdraw/withdrawInfo',
    params: params,
    isControlShowToast: true,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return WithdrawInfoModal.fromJson(response?.data);
  });
}
