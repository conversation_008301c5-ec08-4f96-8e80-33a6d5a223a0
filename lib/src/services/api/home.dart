import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcTemplate.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uuid_util.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

/// SimpleInfo
/// 首页请求账户基本信息接口
Future<SimpleInfoModel> fetchSimpleInfo() {
  return comGetApi(path: '/finance/waimai/account/api/simpleInfo')
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return SimpleInfoModel.fromJson(response?.data);
  });
}

Future<WithdrawDisplayMsgModel> fetchDisplayMsg() async {
  Map param = {"appName": 'waimaie'};
  if (PlatformTool.isNative) {
    dynamic value = await WaimaiENativeBusiness.getEnvironmentInfo();

    param.putIfAbsent("sysName", () => value['wm_ctype']);
    param.putIfAbsent("sysVer", () => value['wm_dversion']);
    param.putIfAbsent("appVer", () => value['version_name']);
    String locCity = '${value['wm_cityid']}_';
    param.putIfAbsent("locCity", () => locCity);
  }

  return comGetApi(
          path: '/finance/v3/h5/api/withdraw/withdrawDisplayMsg', params: param)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return WithdrawDisplayMsgModel.fromJson(response?.data);
  });
}

Future<String> fetchKfUrl() {
  return comPostApi(path: '/gw/api/scs/r/info', params: {"sceneType": 7})
      .then((response) {
    String url = '';
    if (response?.data != null) {
      Map<String, dynamic> json = response.data;
      url = json['scsUrl'];
    }
    return url;
  });
}

// 查询今日实时账单
Future<DailyBillListModel> fetchTodayBill() {
  DateTime today = DateTime.now();
  String str = DateFormat.formatYYYYMMDD(today.millisecondsSinceEpoch);
  Map params = Map();
  params.putIfAbsent('startDailyBillDate', () => str);
  params.putIfAbsent('endDailyBillDate', () => str);
  return comGetApi(
          path: '/finance/v4/h5/api/dailyBill/dailyBillList', params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return DailyBillListModel.fromJson(response?.data);
  });
}

// 查询待结算/已结算
Future<HistorySettleBillModel> fetchHistorySettleBill(int pageNo, Map params) {
  if (params != null) {
    params['pageNo'] = pageNo;
  }

  return comGetApi(
          path: '/finance/waimai/poiSettleBill/api/v2/settleBillList',
          params: params)
      .then((response) async {
    if (response?.data == null) {
      return null;
    }
    return HistorySettleBillModel.fromJson(response?.data);
  });
}

// 查询是否可以开票
Future<int> fetchOpenSesame() {
  return comGetApi(path: '/finance/v4/h5/api/invoice/openSesame')
      .then((response) {
    return response?.data;
  });
}

// 合规检查
Future<bool> fetchShowSpecialItem() {
  return comGetApi(path: '/gw/api/mock/condition').then((response) {
    if (response?.data == null) {
      return true;
    }
    Map<String, dynamic> json = response?.data;
    if (json != null && json['hidden'] == true) {
      return false;
    }
    return true;
  });
}

// 下载账单接口请求
Future<bool> createBillExportTask(Map params) {
  return comGetApi(
          path: '/finance/pc/api/billDownload/createBillExportTask',
          params: params)
      .then((response) {
    if (response?.code != null && response.code == 0) {
      return true;
    } else {
      return false;
    }
  });
}

// AIGC灰度
Future<bool> fetchAigcGray(String scene) {
  return comGetApi(
      path: '/finance/waimai/aigc/api/gray',
      params: {'scene': scene}).then((response) {
    if (response?.code != null && response.code == 0) {
      return response.data;
    } else {
      return false;
    }
  });
}

// AIGC灰度
Future<bool> fetchAigcNewGray() {
  return comGetApi(
      path: '/proxy-gw/aimanager/api/v1/bill/scene/check',
      params: {"businessLine": "1"}).then((response) {
    if (response?.code != null && response.code == 0) {
      if (response.data != null) {
        List result = response.data;
        Map first = result.first;
        return first['check'];
      }
      return false;
    } else {
      return false;
    }
  });
}

// AIGC 模板内容
Future<String> fetchAigcTemplateContent(
    String sceneType, String dailyBillDate) async {
  Map<String, dynamic> params = {};
  // 业务线，外卖标识为1
  params['businessLine'] = 1;
  params['sceneType'] = sceneType;
  params['dailyBillDate'] = dailyBillDate;
  return comPostApi(
          path: '/finance/waimai/aigc/api/template/chat', params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    AigcTemplate template = AigcTemplate.fromJson(response.data);
    return template.content;
  });
}

// AIGC 模板内容
Future<String> fetchAigcTemplateNewContent(
    String sceneType, String dailyBillDate) async {
  Map<String, dynamic> params = {};
  // 业务线，外卖标识为1
  params['businessLine'] = 1;
  params['sceneType'] = "1";
  params['requestId'] = await UuidUtil().getUuidWithHourCache();
  return comGetApi(
          path: '/proxy-gw/aimanager/api/v1/bill/analyse', params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return response.data;
  });
}

// 展示预开票灰度接口
Future<bool> fetchEntryGray() {
  return comGetApi(path: '/gw/api/deliverysettle/invoices/ptb/entry/gray')
      .then((response) {
    if (response?.code != null && response.code == 0) {
      return response?.data['show'];
    } else {
      return false;
    }
  });
}
