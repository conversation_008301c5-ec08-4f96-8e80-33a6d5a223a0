import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
// import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question_icon.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/tag.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/recent_bill.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/accountInfo.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/balanceWithdraw.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/historyFlows.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

class AccountInfo extends StatefulWidget {
  AccountInfo({
    @required this.simpleInfoModel,
    @required this.recentBillList,
    this.onRefreshData,
  });
  final SimpleInfoModel simpleInfoModel;
  final List<HistoryFlowsModel> recentBillList;
  final VoidCallback onRefreshData;

  @override
  AccountInfoState createState() => AccountInfoState();
}

class AccountInfoState extends State<AccountInfo> with HomeMixin, RouteLifecycleStateMixin {
  SimpleInfoModel simpleInfoModel;

  String acctManagerText = '银行卡、推广费、保证金管理';
  // 合规账户
  bool isLimitedAccount = false;
  // 提现状态，0=不可提现（如合并结算），非0=可提现
  int balanceOutStatus = 0;

  @override
  void initState() {
    super.initState();
    fetchShowSpecialItem().then((show) {
      if (show == false) {
        isLimitedAccount = true;
        setState(() {
          acctManagerText = '银行卡、推广费管理';
        });
      }
    });
    // 获取提现状态
    _fetchWithdrawStatus();
  }

  // 获取提现状态
  void _fetchWithdrawStatus() {
    fetchWithdrawInfoSilent({'acctType': 0}).then((WithdrawInfoModal response) {
      if (response != null && mounted) {
        setState(() {
          balanceOutStatus = response.balanceOutStatus ?? 0;
        });
      }
    }).catchError((error) {
      print('fetchWithdrawInfo error: $error');
    });
  }

  @override
  void didAppear() {
    if (widget.onRefreshData != null) {
      widget.onRefreshData();
    }
  }

  @override
  void didDisappear() {}

  // 金服页面跳转（非PC）
  void _jumpToJinFuWithdraw() async {
    final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
    final userInfo = await WaimaiENativeBusiness.getUserInfo();
    String wmPoiId = await Util.getPoiId();

    String host = "https://paymp.meituan.com";
    if (envInfo != null) {
      if (envInfo["hostType"] == "TEST" || envInfo["hostType"] == "QA") {
        // host = "http://paymp.pay.test.sankuai.com";
        host = "http://xiahaoran03-ulyba-sl-paymp.pay.test.sankuai.com";
      }
      if (envInfo["hostType"] == "STAGE") {
        host = "https://paymp.pay.st.sankuai.com";
      }
    }
    
    Map<String, dynamic> param = {
      'source': 16,
      'page': 'waimaiUnifyWithdraw',
      'mwallet_channel': 'finance-reconciliation',
      'fromUnify': 1,
      'type': 'i',
      'poi': wmPoiId ?? '',
      'bizAdminId': userInfo?.acctId ?? '',
      'acctId': userInfo?.acctId ?? '',
      'BSID': userInfo?.accessToken ?? '',
    };
    
    List<String> list = [];
    param.forEach((key, value) {
      list.add('$key=$value');
    });
    String paramStr = list.join('&');
    String finalUrl = '${host}/mwallet/front/common/login-access?$paramStr';
    
    RouteUtils.open(finalUrl);
  }

  // 金服页面跳转（PC端）
  void _jumpToJinFuWithdrawPC() {
    String host = Util.getHost().indexOf('test') >= 0 
        ? "http://paymp.pay.test.sankuai.com" 
        : Util.getHost().indexOf('st.') >= 0 
            ? "https://paymp.pay.st.sankuai.com"
            : "https://paymp.meituan.com";
    
    Map<String, dynamic> param = {
      'source': 16,
      'page': 'waimaiUnifyWithdraw',
      'mwallet_channel': 'finance-reconciliation',
      'fromUnify': 1,
      'type': 'PC',
      'poi': Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId'),
      'bizAdminId': Util.getUrlParam('acctId') ?? Util.getCookie('acctId'),
      'acctId': Util.getUrlParam('acctId') ?? Util.getCookie('acctId'),
      'BSID': Util.getUrlParam('token') ?? Util.getCookie('token'),
    };
    
    List<String> list = [];
    param.forEach((key, value) {
      list.add('$key=$value');
    });
    String paramStr = list.join('&');
    String finalUrl = '${host}/mwallet/front/common/login-access?$paramStr';
    
    // MTFlutterWebUtils.bridgeJump(finalUrl);
    RouteUtils.open(finalUrl);
  }

  _buildAvalableMoney() {
    return Row(
      children: [
        Text(
          '可提现余额 (元）',
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w400,
            fontSize: 12,
          ),
        ),
        RooTooltip(
            lineWordCount: 100,
            target: QuestionIcon(
              color: Color(0xFFFFE862),
            ),
            title: '详情',
            tip:
                '以下情况无法发起手动提现操作：\n\n1. 当前可提现余额小于最低提现金额\n\n2. 当前门店属于多门店合并打款\n\n3. 可提现余额小于等于0')
      ],
    );
  }

  frozenText() {
    return Text(
      '很抱歉，您当前门店的账户已冻结，无法提现。\n如有问题请联系业务经理处理，相关款项会在账户解冻后的下个账期发起提现。\n谢谢！',
      style: TextStyle(
          color: Color(0xFF666666),
          fontWeight: FontWeight.w500,
          fontSize: 14,
          height: 1.5),
    );
  }
  // 去提现按钮
  moneyToBank() {
    /// 参数是接口请求回来的异步数据，一开始是null, 没有做判断，也会使得isFrozen=true
    int asyncFrozenFlag = widget.simpleInfoModel?.isFrozenFlag;
    bool isFrozen = asyncFrozenFlag != null && asyncFrozenFlag != 0;

    // 获取余额
    int balance = widget.simpleInfoModel?.balance ?? 0;
    // 合规账户余额<0时，展示0
    if (isLimitedAccount) {
      balance = balance < 0 ? 0 : balance;
    }

    // 检查是否可以提现（合并结算等情况）
    bool canWithdraw = balanceOutStatus != 0;

    // 余额小于等于0时不展示去提现按钮
    if (balance <= 0) {
      return SizedBox();
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () async {
        if (isFrozen || !canWithdraw) {
          return;
        }
        
        final wmPoiId = await Util.getPoiId();
        final isGrayEnabled = await checkPoiIdGray(wmPoiId ?? '', appSourceType: PlatformTools.isPC ? 2 : 1);
        
        if (isGrayEnabled) {
          if (PlatformTools.isPC) {
            // PC端灰度时跳转金服页面
            _jumpToJinFuWithdrawPC();
          } else {
            // APP端灰度时跳转金服页面
            _jumpToJinFuWithdraw();
          }
        } else {
          // 未灰度时都跳转到Flutter页面
          RouterTools.flutterPageUrl(
            context,
            '/balanceWithdraw',
            params: {"acctType": 0},
          );
        }
        // PlatformTools.isPC
        //     ? MTFlutterWebUtils.bridgeJump(
        //         '/finance/web/balanceWithdraw?acctType=0')
        //     : RouteUtils.open(
        //         'https://waimaieapp.meituan.com/finance/fe/balanceWithdraw?acctType=0');
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ia5lwsyt_mc');
      },
      child: Row(
        children: <Widget>[
          Container(
            // width: 67.5,
            padding: EdgeInsets.fromLTRB(20, 6, 20, 6),
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Center(
              child: Text(
                '去提现',
                textAlign: TextAlign.center,
                strutStyle: StrutStyle(
                  forceStrutHeight: true,
                ),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color((isFrozen || !canWithdraw) ? 0xFFCCCCCC : 0xFF222222),
                ),
              ),
            ),
          ),
          // isFrozen
          //     ? Row(
          //         children: <Widget>[
          //           SizedBox(width: 6),
          //           Question(
          //             onTap: () {
          //               Modal.showModalDialog(context,
          //                   title: '账户已冻结', child: frozenText());
          //             },
          //           )
          //         ],
          //       )
          //     : SizedBox()

          // 提现提示
        ],
      ),
    );
  }

  toBankPage() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        RouterTools.flutterPageUrl(context, '/accountInfo');
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_4fk6m6d5_mc');
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // 账户和图标
          Row(
            children: <Widget>[
              Image.network(
                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/20e6aac46d60abad856eee96b6e1193c/bank-card.png',
                width: 20,
                height: 20,
              ),
              SizedBox(
                width: 4,
              ),
              Text(
                '更多',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              )
            ],
          ),
          // 银行卡、推广费、保证金管理
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                '$acctManagerText',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
                strutStyle: PlatformTool.isWeb
                    ? null
                    : StrutStyle(
                        forceStrutHeight: true,
                        height: 1,
                      ),
              ),
              ArrowIcon(),
            ],
          )
        ],
      ),
    );
  }

  _buildMoneyCnt() {
    int cnt = widget.simpleInfoModel?.balance ?? 0;
    // 合规账户余额<0时，展示0
    if (isLimitedAccount) {
      cnt = cnt < 0 ? 0 : cnt;
    }
    int cntLength = '$cnt'.length;
    double size = 32;
    if (PlatformTool.isPC) {
      size = 35;
    } else {
      if (cntLength >= 7 && cntLength < 9) {
        size = 24;
      } else if (cntLength > 9) {
        size = 18;
      }
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            // PC下不触发点击跳转
            if (PlatformTools.isPC) return;
            RouterTools.flutterPageUrl(context, '/balanceFlow');
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_vh5j1p87_mc');
          },
          child: Row(
            children: <Widget>[
              Text(
                MoneyTool.formatMoney(cnt),
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w600,
                  fontSize: size,
                ),
              ),
              ResponsiveSystem(
                app: ArrowIcon(),
              ),
              ResponsiveSystem(
                app: settleTypeWidget(),
              ),
            ],
          ),
        ),
        ResponsiveSystem(
          app: moneyToBank(),
        ),
      ],
    );
  }

  // 结算类型标签
  settleTypeWidget() {
    // 获取余额
    int balance = widget.simpleInfoModel?.balance ?? 0;
    // 合规账户余额<0时，展示0
    if (isLimitedAccount) {
      balance = balance < 0 ? 0 : balance;
    }
    
    // 余额小于等于0时不展示结算类型标签
    if (balance <= 0) {
      return SizedBox();
    }

    String type = '';
    switch (widget.simpleInfoModel?.settleType) {
      case 1:
        type = '自动提现';
        break;
      case 2:
        type = '自动汇入账户';
        break;
      case 3:
        type = '手动提现';
        break;
    }
    if (type != '') {
      return Tag(text: type);
    }
    return SizedBox();
  }

  _buildPoiColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 可提现金额文本
        _buildAvalableMoney(),
        SizedBox(height: 5),
        // 真正的钱数
        _buildMoneyCnt(),
        // 空白区域
        SizedBox(
          height: ResponsiveSystem.bothAppPc(
            runApp: 37.0,
            runPc: 30.0,
          ),
        ),
        ResponsiveSystem(
          pc: moneyToBank(),
        ),
        ResponsiveSystem(
          app: toBankPage(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSystem(
      app: Container(
        margin: EdgeInsets.fromLTRB(0, 13, 0, 0),
        padding: EdgeInsets.fromLTRB(16, 24, 16, 24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.5),
          gradient: yellowGradient,
        ),
        child: _buildPoiColumn(),
      ),
      pc: Container(
        margin: EdgeInsets.fromLTRB(0, 1, 0, 0),
        padding: EdgeInsets.fromLTRB(30, 24, 16, 24),
        decoration: BoxDecoration(
          image: DecorationImage(
              image: NetworkImage(
                'http://p0.meituan.net/tuling/44daaeafe9604c18cb4b9fa2886ff47337829.png',
              ),
              fit: BoxFit.fill),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(6),
            bottomRight: Radius.circular(6),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: 400,
              child: _buildPoiColumn(),
            ),
            RecentBillListPage(
              recentBillList: widget?.recentBillList ?? [],
            ),
          ],
        ),
      ),
    );
  }
}
